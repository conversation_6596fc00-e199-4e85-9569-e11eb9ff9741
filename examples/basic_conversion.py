"""Basic example of converting an OpenAPI spec to MCP tools."""

import asyncio
from pathlib import Path
from openapi_mcp import OpenAPIToMCPConverter


async def main():
    """Run basic conversion example."""
    # Example 1: Convert from local file
    print("Converting from local OpenAPI spec...")
    
    # Assuming you have an OpenAPI spec file
    spec_path = Path("path/to/your/openapi.yaml")
    
    if spec_path.exists():
        # Note: This example would need a local OpenAPI spec file
        # For now, we'll skip this part
        print(f"Local spec file not found at: {spec_path}")
        print("Skipping local file example...")

    # Example 2: Use a real API (JSONPlaceholder)
    print("\n\nConverting from JSONPlaceholder API...")

    # Use JSONPlaceholder API which has an OpenAPI spec
    converter = OpenAPIToMCPConverter(
        openapi_url="https://jsonplaceholder.typicode.com/openapi.json",
        name="JSONPlaceholder API",
        description="Fake REST API for testing and prototyping"
    )

    try:
        # Initialize
        await converter.initialize()

        # Get API info
        info = converter.get_api_info()
        print(f"API: {info['title']} v{info['version']}")

        # List tools
        tools = converter.list_tools()
        print(f"Generated {len(tools)} tools:")
        for tool in tools[:5]:  # Show first 5
            print(f"  - {tool['name']}: {tool['description']}")

        print(f"\nConverter created successfully with {len(tools)} tools!")

    except Exception as e:
        print(f"Error with JSONPlaceholder API: {e}")
        print("Falling back to Petstore API...")

        # Fallback to Petstore API
        converter = OpenAPIToMCPConverter(
            openapi_url="https://petstore3.swagger.io/api/v3/openapi.json",
            name="Petstore API",
            description="Example pet store API"
        )

        await converter.initialize()

        # Get API info
        info = converter.get_api_info()
        print(f"API: {info['title']} v{info['version']}")

        # List tools
        tools = converter.list_tools()
        print(f"Generated {len(tools)} tools:")
        for tool in tools[:5]:  # Show first 5
            print(f"  - {tool['name']}: {tool['description']}")

        print(f"\nConverter created successfully with {len(tools)} tools!")


if __name__ == "__main__":
    asyncio.run(main())