"""Basic example of converting an OpenAPI spec to MCP tools."""

import asyncio
from pathlib import Path
from openapi_mcp import OpenAPIMCPConverter


async def main():
    """Run basic conversion example."""
    # Example 1: Convert from local file
    print("Converting from local OpenAPI spec...")
    
    # Assuming you have an OpenAPI spec file
    spec_path = Path("path/to/your/openapi.yaml")
    
    if spec_path.exists():
        converter = OpenAPIMCPConverter(spec_path=spec_path)
        
        # List available operations
        operations = converter.list_operations()
        print(f"\nFound {len(operations)} operations:")
        for op in operations[:5]:  # Show first 5
            print(f"  - {op['method']} {op['path']}: {op['summary']}")
        
        # Convert to MCP server
        mcp_server = converter.convert()
        print(f"\nMCP server created with {len(operations)} tools")
    else:
        print(f"Please provide a valid OpenAPI spec file at: {spec_path}")
    
    # Example 2: Convert from dictionary
    print("\n\nConverting from OpenAPI dictionary...")
    
    # Minimal OpenAPI spec for demonstration
    openapi_dict = {
        "openapi": "3.0.0",
        "info": {
            "title": "Example API",
            "version": "1.0.0"
        },
        "servers": [
            {"url": "https://api.example.com"}
        ],
        "paths": {
            "/users": {
                "get": {
                    "operationId": "getUsers",
                    "summary": "Get all users",
                    "parameters": [
                        {
                            "name": "limit",
                            "in": "query",
                            "description": "Maximum number of users to return",
                            "required": False,
                            "schema": {
                                "type": "integer",
                                "default": 10
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "List of users"
                        }
                    }
                }
            },
            "/users/{id}": {
                "get": {
                    "operationId": "getUser",
                    "summary": "Get a user by ID",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "description": "User ID",
                            "required": True,
                            "schema": {
                                "type": "string"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "User details"
                        }
                    }
                }
            }
        }
    }
    
    converter = OpenAPIMCPConverter(spec_dict=openapi_dict)
    
    # List operations
    operations = converter.list_operations()
    print("Operations in example API:")
    for op in operations:
        print(f"  - {op['operation_id']}: {op['method']} {op['path']}")
    
    # Convert to MCP
    mcp_server = converter.convert("example_api")
    print(f"\nMCP server 'example_api' created successfully!")


if __name__ == "__main__":
    asyncio.run(main())