#!/usr/bin/env python3
"""
Simple test of the OpenAPI to MCP converter with a public API.
This example uses the Petstore API for testing without authentication.
"""

import asyncio
from openapi_mcp import OpenAPIToMCPConverter

async def main():
    """Test the converter with the Petstore API."""
    
    # Use the public Petstore API for testing
    petstore_url = "https://petstore3.swagger.io/api/v3/openapi.json"
    
    print("🐾 Testing OpenAPI to MCP Converter with Petstore API...")
    
    # Create converter
    converter = OpenAPIToMCPConverter(
        openapi_url=petstore_url,
        name="Petstore API",
        description="Example pet store API converted to MCP",
        config={
            "tool_name_prefix": "pet_",
            "naming_strategy": "operationId",
            "timeout": 10.0,
        }
    )
    
    # Initialize
    print("\n📦 Initializing converter...")
    await converter.initialize()
    
    # Get API info
    info = converter.get_api_info()
    print(f"\n📊 API Info:")
    print(f"  Title: {info.get('title')}")
    print(f"  Version: {info.get('version')}")
    
    # List some tools
    tools = converter.list_tools()
    print(f"\n🔧 Generated {len(tools)} tools")
    print("\nFirst 10 tools:")
    for tool in tools[:10]:
        print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
    
    # Test a simple GET request
    print("\n🧪 Testing tool execution...")
    try:
        # Find available pets
        result = await converter.call_tool(
            "pet_findPetsByStatus",
            status="available"
        )
        print(f"✅ Found {len(result) if isinstance(result, list) else 'some'} available pets")
    except Exception as e:
        print(f"❌ Error calling tool: {e}")
    
    print("\n✨ Test complete!")

if __name__ == "__main__":
    asyncio.run(main())