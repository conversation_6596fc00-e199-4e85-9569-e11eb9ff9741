#!/usr/bin/env python3
"""
Example: Converting PulseTrack API to MCP Server

This example demonstrates how to use the OpenAPI to MCP converter
with the PulseTrack medical platform API.
"""

import asyncio
import os
from dotenv import load_dotenv
from openapi_mcp import OpenAPIToMCPConverter

# Load environment variables
load_dotenv()

async def main():
    """Main example demonstrating PulseTrack API conversion to MCP."""
    
    # PulseTrack API OpenAPI URL
    pulsetrack_openapi_url = "http://pulsetrack-api.synapsedx.co.uk:8000/openapi.json"
    
    # Configure authentication
    # You can use environment variables or pass directly
    auth_config = {
        "type": "bearer",
        "token": os.getenv("PULSETRACK_API_KEY", "your-api-key-here")
    }
    
    # Alternative: API key authentication
    # auth_config = {
    #     "type": "apiKey",
    #     "location": "header",
    #     "name": "X-API-Key",
    #     "value": os.getenv("PULSETRACK_API_KEY")
    # }
    
    # Create converter with configuration
    converter = OpenAPIToMCPConverter(
        openapi_url=pulsetrack_openapi_url,
        name="PulseTrack Medical Platform",
        description="MCP interface for PulseTrack healthcare API",
        auth_config=auth_config,
        config={
            # Tool naming configuration
            "tool_name_prefix": "pulsetrack_",
            "naming_strategy": "operationId",  # or "path_method"
            
            # Filtering options
            "include_tags": ["Patients", "Clinicians", "Appointments"],  # Only include specific tags
            # "exclude_patterns": ["*/admin/*"],  # Exclude admin endpoints
            # "include_methods": ["GET", "POST"],  # Only include specific HTTP methods
            
            # HTTP client configuration
            "timeout": 30.0,
            "max_retries": 3,
            "retry_delay": 1.0,
            
            # Rate limiting (optional)
            # "rate_limit": {
            #     "requests_per_second": 10,
            #     "burst": 20
            # },
            
            # Base URL override (if needed)
            # "base_url_override": "https://api.pulsetrack.com",
        }
    )
    
    # Initialize the converter
    print("🏥 Initializing PulseTrack MCP Server...")
    await converter.initialize()
    
    # Get information about the converted API
    info = converter.get_api_info()
    print(f"\n📊 API Information:")
    print(f"  - Title: {info.get('title', 'N/A')}")
    print(f"  - Version: {info.get('version', 'N/A')}")
    print(f"  - Description: {info.get('description', 'N/A')}")
    
    # List generated tools
    tools = converter.list_tools()
    print(f"\n🔧 Generated {len(tools)} MCP tools:")
    
    # Group tools by tag
    tools_by_tag = {}
    for tool in tools[:20]:  # Show first 20 tools
        tags = tool.get('tags', ['Other'])
        for tag in tags:
            if tag not in tools_by_tag:
                tools_by_tag[tag] = []
            tools_by_tag[tag].append(tool)
    
    # Display tools organized by tag
    for tag, tag_tools in tools_by_tag.items():
        print(f"\n  {tag}:")
        for tool in tag_tools:
            print(f"    - {tool['name']}: {tool.get('description', 'No description')}")
    
    # Example: Using a generated tool
    print("\n\n🚀 Testing MCP Tools:")
    
    # Example 1: Get patient profile (if authenticated as patient)
    try:
        print("\n1️⃣ Testing patient profile retrieval...")
        result = await converter.call_tool("pulsetrack_read_patient_me_api_v1_patients_me_get")
        print(f"   ✅ Patient profile: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Example 2: List medications for patient's clinic
    try:
        print("\n2️⃣ Testing medication list retrieval...")
        result = await converter.call_tool("pulsetrack_get_clinic_medications_for_patient_api_v1_patients_me_medications_get")
        print(f"   ✅ Medications: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Example 3: Get appointments with parameters
    try:
        print("\n3️⃣ Testing appointment retrieval with limit...")
        result = await converter.call_tool(
            "pulsetrack_read_patient_appointments_api_v1_patients_me_appointments_get",
            limit=5
        )
        print(f"   ✅ Appointments: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Start the MCP server
    print("\n\n🎯 Starting MCP Server...")
    print("The MCP server is now ready to handle requests!")
    print("Tools can be called via the MCP protocol.")
    
    # In a real application, you would start the MCP server here
    # await converter.start()
    
    # For this example, we'll just demonstrate the setup
    print("\n✨ PulseTrack API successfully converted to MCP server!")
    print(f"📍 {len(tools)} tools are now available via MCP protocol")

if __name__ == "__main__":
    asyncio.run(main())