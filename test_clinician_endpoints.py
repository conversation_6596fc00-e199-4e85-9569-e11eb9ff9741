#!/usr/bin/env python3
"""
Test specific clinician endpoints in PulseTrack API
"""

import asyncio
from openapi_mcp import OpenAPIToMCPConverter

async def main():
    """Test clinician-specific endpoints."""
    
    # Your PulseTrack API key (clinician access)
    api_key = "pk_live_0Jt9rPfIg6SVR3l8eqq8biELPR62THApGWs1JX57"
    
    print("👨‍⚕️ Testing PulseTrack API with clinician authentication...")
    
    # Create converter
    converter = OpenAPIToMCPConverter(
        openapi_url="http://pulsetrack-api.synapsedx.co.uk:8000/openapi.json",
        name="PulseTrack Medical Platform",
        auth_config={
            "type": "bearer",
            "token": api_key
        },
        config={
            "tool_name_prefix": "pulsetrack_",
            "timeout": 30.0,
        }
    )
    
    # Initialize
    await converter.initialize()
    tools = converter.list_tools()
    
    print(f"\n🔧 Generated {len(tools)} MCP tools")
    
    # Find and test specific clinician endpoints
    print("\n🧪 Testing clinician-specific endpoints:")
    
    # Test 1: List patients (clinician view)
    try:
        print("\n1️⃣ Testing patient list (clinician view)...")
        result = await converter.call_tool("pulsetrack_read_patients_by_clinician_api_v1_clinicians_patients_get")
        print(f"   ✅ Patient list retrieved successfully!")
        print(f"   👥 Patients: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Get medication requests (clinician view)
    try:
        print("\n2️⃣ Testing medication requests (clinician view)...")
        result = await converter.call_tool("pulsetrack_read_medication_requests_by_clinician_api_v1_clinicians_medication_requests_get")
        print(f"   ✅ Medication requests retrieved successfully!")
        print(f"   💊 Medication requests: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: List API keys
    try:
        print("\n3️⃣ Testing API key management...")
        result = await converter.call_tool("pulsetrack_list_api_keys_api_v1_api_keys__get")
        print(f"   ✅ API keys retrieved successfully!")
        print(f"   🔑 API keys: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Create access code for patient
    try:
        print("\n4️⃣ Testing access code generation...")
        result = await converter.call_tool("pulsetrack_create_access_code_api_v1_clinicians_access_codes_post")
        print(f"   ✅ Access code created successfully!")
        print(f"   🎫 Access code: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Show all available clinician tools
    print("\n\n📋 All available clinician tools:")
    clinician_tools = [tool for tool in tools if 'clinician' in tool['name'].lower()]
    for i, tool in enumerate(clinician_tools, 1):
        print(f"  {i:2d}. {tool['name']}")
        print(f"      {tool.get('description', 'No description')}")
    
    # Show API management tools
    print("\n\n🔑 API management tools:")
    api_tools = [tool for tool in tools if 'api_key' in tool['name'].lower()]
    for i, tool in enumerate(api_tools, 1):
        print(f"  {i:2d}. {tool['name']}")
        print(f"      {tool.get('description', 'No description')}")
    
    print(f"\n\n✨ Clinician API testing complete!")
    print(f"🎯 Total tools available: {len(tools)}")
    print(f"👨‍⚕️ Clinician-specific tools: {len(clinician_tools)}")
    print(f"🔑 API management tools: {len(api_tools)}")

if __name__ == "__main__":
    asyncio.run(main())
