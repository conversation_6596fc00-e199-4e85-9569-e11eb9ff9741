"""Tests for OpenAPI to MCP converter."""

import pytest
from openapi_mcp import OpenAPIMCPConverter, ConversionConfig


def test_converter_initialization():
    """Test converter can be initialized with a spec dictionary."""
    spec_dict = {
        "openapi": "3.0.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "servers": [{"url": "https://api.example.com"}],
        "paths": {}
    }
    
    converter = OpenAPIMCPConverter(spec_dict=spec_dict)
    assert converter.spec_dict == spec_dict
    assert converter.spec.openapi == "3.0.0"


def test_converter_requires_spec():
    """Test converter raises error without spec."""
    with pytest.raises(ValueError, match="Either spec_path or spec_dict must be provided"):
        OpenAPIMCPConverter()


def test_list_operations():
    """Test listing operations from spec."""
    spec_dict = {
        "openapi": "3.0.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "servers": [{"url": "https://api.example.com"}],
        "paths": {
            "/users": {
                "get": {
                    "operationId": "getUsers",
                    "summary": "Get all users",
                    "tags": ["users"],
                    "responses": {"200": {"description": "Success"}}
                }
            }
        }
    }
    
    converter = OpenAPIMCPConverter(spec_dict=spec_dict)
    operations = converter.list_operations()
    
    assert len(operations) == 1
    assert operations[0]['operation_id'] == 'getUsers'
    assert operations[0]['method'] == 'GET'
    assert operations[0]['path'] == '/users'
    assert operations[0]['summary'] == 'Get all users'
    assert operations[0]['tags'] == ['users']


def test_conversion_config():
    """Test converter with custom configuration."""
    config = ConversionConfig(
        base_url="https://custom.api.com",
        include_tags=["users"],
        parameter_style="snake_case"
    )
    
    spec_dict = {
        "openapi": "3.0.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "paths": {}
    }
    
    converter = OpenAPIMCPConverter(spec_dict=spec_dict, config=config)
    assert converter.config.base_url == "https://custom.api.com"
    assert converter.config.include_tags == ["users"]
    assert converter.config.parameter_style == "snake_case"