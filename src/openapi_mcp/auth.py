"""Authentication handlers for OpenAPI requests."""

import base64
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

import httpx

logger = logging.getLogger(__name__)


class AuthType(Enum):
    """Supported authentication types."""
    NONE = "none"
    API_KEY = "api_key"
    BEARER = "bearer"
    BASIC = "basic"
    OAUTH2 = "oauth2"
    CUSTOM = "custom"


@dataclass
class AuthConfig:
    """Authentication configuration."""
    auth_type: AuthType = AuthType.NONE
    
    # API Key auth
    api_key: Optional[str] = None
    api_key_header: str = "X-API-Key"
    api_key_location: str = "header"  # header, query, cookie
    
    # Bearer token auth
    bearer_token: Optional[str] = None
    
    # Basic auth
    username: Optional[str] = None
    password: Optional[str] = None
    
    # OAuth2
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    token_url: Optional[str] = None
    scope: Optional[str] = None
    grant_type: str = "client_credentials"
    
    # Custom auth
    custom_headers: Dict[str, str] = None
    custom_params: Dict[str, str] = None
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.custom_headers is None:
            self.custom_headers = {}
        if self.custom_params is None:
            self.custom_params = {}
        
        # Convert string auth_type to enum
        if isinstance(self.auth_type, str):
            self.auth_type = AuthType(self.auth_type.lower())


class AuthHandler:
    """Handle authentication for API requests."""
    
    def __init__(self, config: AuthConfig):
        """Initialize auth handler.
        
        Args:
            config: Authentication configuration
        """
        self.config = config
        self._oauth2_token: Optional[str] = None
        self._oauth2_token_expires: Optional[float] = None
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers.
        
        Returns:
            Dictionary of authentication headers
        """
        headers = {}
        
        if self.config.auth_type == AuthType.NONE:
            return headers
        
        elif self.config.auth_type == AuthType.API_KEY:
            if self.config.api_key_location == "header":
                headers[self.config.api_key_header] = self.config.api_key
        
        elif self.config.auth_type == AuthType.BEARER:
            if self.config.bearer_token:
                headers["Authorization"] = f"Bearer {self.config.bearer_token}"
        
        elif self.config.auth_type == AuthType.BASIC:
            if self.config.username and self.config.password:
                credentials = f"{self.config.username}:{self.config.password}"
                encoded = base64.b64encode(credentials.encode()).decode()
                headers["Authorization"] = f"Basic {encoded}"
        
        elif self.config.auth_type == AuthType.OAUTH2:
            token = await self._get_oauth2_token()
            if token:
                headers["Authorization"] = f"Bearer {token}"
        
        elif self.config.auth_type == AuthType.CUSTOM:
            headers.update(self.config.custom_headers)
        
        return headers
    
    async def get_auth_params(self) -> Dict[str, str]:
        """Get authentication query parameters.
        
        Returns:
            Dictionary of authentication parameters
        """
        params = {}
        
        if self.config.auth_type == AuthType.API_KEY:
            if self.config.api_key_location == "query":
                params[self.config.api_key_header] = self.config.api_key
        
        elif self.config.auth_type == AuthType.CUSTOM:
            params.update(self.config.custom_params)
        
        return params
    
    async def _get_oauth2_token(self) -> Optional[str]:
        """Get OAuth2 access token.
        
        Returns:
            Access token or None
        """
        # TODO: Implement token caching and refresh logic
        if self._oauth2_token:
            return self._oauth2_token
        
        # Request new token
        try:
            async with httpx.AsyncClient() as client:
                data = {
                    "grant_type": self.config.grant_type,
                    "client_id": self.config.client_id,
                    "client_secret": self.config.client_secret
                }
                
                if self.config.scope:
                    data["scope"] = self.config.scope
                
                response = await client.post(
                    self.config.token_url,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                response.raise_for_status()
                token_data = response.json()
                
                self._oauth2_token = token_data.get("access_token")
                # TODO: Handle token expiration
                
                return self._oauth2_token
                
        except Exception as e:
            logger.error(f"Failed to get OAuth2 token: {e}")
            return None
    
    def update_token(self, token: str) -> None:
        """Update the authentication token.
        
        Args:
            token: New token value
        """
        if self.config.auth_type == AuthType.BEARER:
            self.config.bearer_token = token
        elif self.config.auth_type == AuthType.OAUTH2:
            self._oauth2_token = token
        else:
            logger.warning(f"Token update not supported for auth type: {self.config.auth_type}")
    
    def update_api_key(self, api_key: str) -> None:
        """Update the API key.
        
        Args:
            api_key: New API key
        """
        if self.config.auth_type == AuthType.API_KEY:
            self.config.api_key = api_key
        else:
            logger.warning("API key update requested but auth type is not API_KEY")