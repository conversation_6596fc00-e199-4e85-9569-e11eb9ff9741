"""Main converter class for OpenAPI to MCP conversion."""

from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from fastmcp import MCP

from .parser import OpenAPIParser
from .generator import MCPToolGenerator
from .client import HTTPClient
from .auth import AuthFactory, AuthHandler
from .types import ConversionConfig, OpenAPISpec


class OpenAPIMCPConverter:
    """Converter for OpenAPI specifications to MCP tools."""

    def __init__(
        self,
        spec_path: Optional[Union[str, Path]] = None,
        spec_dict: Optional[Dict[str, Any]] = None,
        base_url: Optional[str] = None,
        auth_handler: Optional[AuthHandler] = None,
        config: Optional[ConversionConfig] = None
    ):
        """Initialize the converter.
        
        Args:
            spec_path: Path to OpenAPI specification file
            spec_dict: OpenAPI specification as dictionary
            base_url: Base URL for API requests (overrides spec)
            auth_handler: Authentication handler
            config: Conversion configuration
        """
        if not spec_path and not spec_dict:
            raise ValueError("Either spec_path or spec_dict must be provided")
        
        self.spec_path = spec_path
        self.spec_dict = spec_dict
        self.base_url = base_url
        self.auth_handler = auth_handler
        self.config = config or ConversionConfig()
        
        # Initialize components
        self.parser = OpenAPIParser()
        self.generator = MCPToolGenerator(self.config)
        self._spec: Optional[OpenAPISpec] = None
        self._http_client: Optional[HTTPClient] = None

    @property
    def spec(self) -> OpenAPISpec:
        """Get the parsed OpenAPI specification."""
        if self._spec is None:
            if self.spec_path:
                self._spec = self.parser.parse(self.spec_path)
            else:
                self._spec = self.parser.parse_from_dict(self.spec_dict)
        return self._spec

    @property
    def http_client(self) -> HTTPClient:
        """Get the HTTP client."""
        if self._http_client is None:
            # Determine base URL
            if self.base_url:
                base_url = self.base_url
            elif self.spec.servers:
                base_url = self.spec.servers[0].get('url', '')
            else:
                raise ValueError("No base URL found in spec or configuration")
            
            # Create HTTP client
            self._http_client = HTTPClient(
                base_url=base_url,
                auth_handler=self.auth_handler
            )
        
        return self._http_client

    def convert(self, mcp_server_name: str = "openapi_mcp") -> MCP:
        """Convert OpenAPI spec to MCP server.
        
        Args:
            mcp_server_name: Name for the MCP server
            
        Returns:
            MCP server instance with generated tools
        """
        # Create MCP server
        mcp = MCP(mcp_server_name)
        
        # Extract operations
        operations = self.parser.extract_operations(self.spec)
        
        # Generate and register tools
        for op_data in operations:
            tool = self.generator.generate_tool(op_data, self.http_client)
            if tool:
                # Create MCP function
                mcp_func = self.generator.create_mcp_function(tool, self.http_client)
                
                # Register with MCP
                # Build parameter schema
                param_schema = {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
                
                for param in tool.parameters:
                    prop_schema = {"type": param.type}
                    if param.description:
                        prop_schema["description"] = param.description
                    if param.enum:
                        prop_schema["enum"] = param.enum
                    if param.default is not None:
                        prop_schema["default"] = param.default
                    
                    param_schema["properties"][param.name] = prop_schema
                    
                    if param.required:
                        param_schema["required"].append(param.name)
                
                # Register the tool
                mcp.tool(
                    name=tool.name,
                    description=tool.description,
                    params=param_schema
                )(mcp_func)
        
        return mcp

    def list_operations(self) -> List[Dict[str, Any]]:
        """List all operations in the OpenAPI spec.
        
        Returns:
            List of operation summaries
        """
        operations = self.parser.extract_operations(self.spec)
        summaries = []
        
        for op_data in operations:
            operation = op_data['operation']
            summary = {
                'operation_id': op_data['operation_id'],
                'method': op_data['method'].value,
                'path': op_data['path'],
                'summary': operation.get('summary', ''),
                'description': operation.get('description', ''),
                'tags': operation.get('tags', []),
                'requires_auth': bool(operation.get('security'))
            }
            summaries.append(summary)
        
        return summaries

    def get_security_schemes(self) -> Dict[str, Any]:
        """Get security schemes from the spec.
        
        Returns:
            Dictionary of security schemes
        """
        return self.parser.extract_security_schemes(self.spec)

    @classmethod
    def from_url(
        cls,
        url: str,
        auth_handler: Optional[AuthHandler] = None,
        config: Optional[ConversionConfig] = None
    ) -> "OpenAPIMCPConverter":
        """Create converter by fetching OpenAPI spec from URL.
        
        Args:
            url: URL to fetch OpenAPI spec from
            auth_handler: Optional authentication handler
            config: Optional conversion configuration
            
        Returns:
            Converter instance
        """
        import httpx
        
        # Fetch the spec
        response = httpx.get(url)
        response.raise_for_status()
        
        # Parse JSON or YAML
        if url.endswith('.yaml') or url.endswith('.yml'):
            import yaml
            spec_dict = yaml.safe_load(response.text)
        else:
            spec_dict = response.json()
        
        # Extract base URL from spec if not in config
        base_url = None
        if config and config.base_url:
            base_url = config.base_url
        elif 'servers' in spec_dict and spec_dict['servers']:
            base_url = spec_dict['servers'][0]['url']
        
        return cls(
            spec_dict=spec_dict,
            base_url=base_url,
            auth_handler=auth_handler,
            config=config
        )

    def create_auth_from_env(self, env_prefix: str = "") -> Optional[AuthHandler]:
        """Create authentication handler from environment variables.
        
        Args:
            env_prefix: Prefix for environment variables
            
        Returns:
            Authentication handler or None
        """
        schemes = self.get_security_schemes()
        
        # Try to create auth handler for the first scheme
        for scheme_name, scheme in schemes.items():
            handler = AuthFactory.create_from_env(scheme_name, scheme, env_prefix)
            if handler:
                return handler
        
        return None