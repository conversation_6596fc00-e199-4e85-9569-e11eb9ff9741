"""Main converter class for OpenAPI to MCP conversion."""

import asyncio
import os
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from enum import Enum
import httpx
from fastmcp import FastMCP

from .parser import OpenAPIParser
from .client import OpenAPIClient
from .auth import <PERSON><PERSON><PERSON><PERSON><PERSON>, Auth<PERSON>onfig, AuthType


class NamingStrategy(str, Enum):
    """Tool naming strategies."""
    OPERATION_ID = "operationId"
    PATH_METHOD = "path_method"


class ConverterConfig:
    """Configuration for the OpenAPI to MCP converter."""

    def __init__(
        self,
        tool_name_prefix: str = "",
        tool_name_suffix: str = "",
        naming_strategy: NamingStrategy = NamingStrategy.OPERATION_ID,
        include_tags: Optional[List[str]] = None,
        exclude_tags: Optional[List[str]] = None,
        include_patterns: Optional[List[str]] = None,
        exclude_patterns: Optional[List[str]] = None,
        include_methods: Optional[List[str]] = None,
        exclude_methods: Optional[List[str]] = None,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        verify_ssl: bool = True,
        rate_limit: Optional[Dict[str, Any]] = None
    ):
        self.tool_name_prefix = tool_name_prefix
        self.tool_name_suffix = tool_name_suffix
        self.naming_strategy = naming_strategy
        self.include_tags = include_tags or []
        self.exclude_tags = exclude_tags or []
        self.include_patterns = include_patterns or []
        self.exclude_patterns = exclude_patterns or []
        self.include_methods = include_methods or []
        self.exclude_methods = exclude_methods or []
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.verify_ssl = verify_ssl
        self.rate_limit = rate_limit or {}


class OpenAPIToMCPConverter:
    """Converter for OpenAPI specifications to MCP tools."""

    def __init__(
        self,
        openapi_url: Optional[str] = None,
        name: str = "OpenAPI MCP Server",
        description: Optional[str] = None,
        auth_config: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """Initialize the converter.

        Args:
            openapi_url: URL to OpenAPI specification
            name: Name for the MCP server
            description: Description of the MCP server
            auth_config: Authentication configuration
            config: Additional configuration options
        """
        self.openapi_url = openapi_url
        self.name = name
        self.description = description or f"MCP server for {name}"

        # Parse auth config
        self.auth_config = self._parse_auth_config(auth_config or {})

        # Parse converter config
        config_dict = config or {}
        self.config = ConverterConfig(
            tool_name_prefix=config_dict.get("tool_name_prefix", ""),
            tool_name_suffix=config_dict.get("tool_name_suffix", ""),
            naming_strategy=NamingStrategy(config_dict.get("naming_strategy", "operationId")),
            include_tags=config_dict.get("include_tags"),
            exclude_tags=config_dict.get("exclude_tags"),
            include_patterns=config_dict.get("include_patterns"),
            exclude_patterns=config_dict.get("exclude_patterns"),
            include_methods=config_dict.get("include_methods"),
            exclude_methods=config_dict.get("exclude_methods"),
            timeout=config_dict.get("timeout", 30.0),
            max_retries=config_dict.get("max_retries", 3),
            retry_delay=config_dict.get("retry_delay", 1.0),
            verify_ssl=config_dict.get("verify_ssl", True),
            rate_limit=config_dict.get("rate_limit", {})
        )

        # Initialize components
        self.parser = OpenAPIParser()
        self._spec: Optional[Dict[str, Any]] = None
        self._client: Optional[OpenAPIClient] = None
        self._mcp_server: Optional[FastMCP] = None
        self._tools: List[Dict[str, Any]] = []

    def _parse_auth_config(self, auth_config: Dict[str, Any]) -> AuthConfig:
        """Parse authentication configuration."""
        if not auth_config:
            return AuthConfig(auth_type=AuthType.NONE)

        auth_type_str = auth_config.get("type", "none")

        if auth_type_str == "bearer":
            return AuthConfig(
                auth_type=AuthType.BEARER,
                bearer_token=auth_config.get("token")
            )
        elif auth_type_str == "apiKey":
            return AuthConfig(
                auth_type=AuthType.API_KEY,
                api_key=auth_config.get("value"),
                api_key_header=auth_config.get("name", "X-API-Key"),
                api_key_location=auth_config.get("location", "header")
            )
        elif auth_type_str == "basic":
            return AuthConfig(
                auth_type=AuthType.BASIC,
                username=auth_config.get("username"),
                password=auth_config.get("password")
            )
        else:
            return AuthConfig(auth_type=AuthType.NONE)

    async def initialize(self):
        """Initialize the converter by fetching and parsing the OpenAPI spec."""
        if not self.openapi_url:
            raise ValueError("OpenAPI URL is required")

        # Fetch the OpenAPI spec
        async with httpx.AsyncClient() as client:
            response = await client.get(self.openapi_url)
            response.raise_for_status()
            self._spec = response.json()

        # Create HTTP client for API calls
        base_url = self._get_base_url()
        auth_handler = AuthHandler(self.auth_config) if self.auth_config.auth_type != AuthType.NONE else None

        self._client = OpenAPIClient(
            base_url=base_url,
            auth_handler=auth_handler,
            timeout=self.config.timeout,
            max_retries=self.config.max_retries
        )

        # Generate tools
        self._generate_tools()

    def _get_base_url(self) -> str:
        """Get the base URL from the OpenAPI spec."""
        if not self._spec:
            raise ValueError("OpenAPI spec not loaded")

        servers = self._spec.get("servers", [])
        if servers:
            server_url = servers[0]["url"]

            # If the server URL is relative, construct it from the OpenAPI spec URL
            if server_url.startswith("/") and self.openapi_url:
                from urllib.parse import urlparse, urljoin
                parsed_openapi_url = urlparse(self.openapi_url)
                base_host = f"{parsed_openapi_url.scheme}://{parsed_openapi_url.netloc}"
                return urljoin(base_host, server_url)
            else:
                return server_url
        else:
            # Fallback: derive base URL from the OpenAPI spec URL
            if self.openapi_url:
                from urllib.parse import urlparse
                parsed_url = urlparse(self.openapi_url)
                # Remove the path to get just the base URL
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                return base_url
            else:
                raise ValueError("No servers found in OpenAPI spec and no OpenAPI URL provided")

    def _generate_tools(self):
        """Generate MCP tools from OpenAPI operations."""
        if not self._spec:
            raise ValueError("OpenAPI spec not loaded")

        paths = self._spec.get("paths", {})

        for path, path_item in paths.items():
            for method, operation in path_item.items():
                if method.upper() not in ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]:
                    continue

                # Apply filters
                if not self._should_include_operation(operation, method, path):
                    continue

                # Generate tool
                tool = self._create_tool(path, method, operation)
                if tool:
                    self._tools.append(tool)

    def _should_include_operation(self, operation: Dict[str, Any], method: str, path: str) -> bool:
        """Check if operation should be included based on filters."""
        # Check tags
        tags = operation.get("tags", [])
        if self.config.include_tags and not any(tag in self.config.include_tags for tag in tags):
            return False
        if self.config.exclude_tags and any(tag in self.config.exclude_tags for tag in tags):
            return False

        # Check methods
        if self.config.include_methods and method.upper() not in self.config.include_methods:
            return False
        if self.config.exclude_methods and method.upper() in self.config.exclude_methods:
            return False

        return True

    def _create_tool(self, path: str, method: str, operation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a tool definition from an OpenAPI operation."""
        operation_id = operation.get("operationId")
        if not operation_id and self.config.naming_strategy == NamingStrategy.OPERATION_ID:
            # Generate operation ID from path and method
            operation_id = f"{method.lower()}_{path.replace('/', '_').replace('{', '').replace('}', '').strip('_')}"

        # Generate tool name
        if self.config.naming_strategy == NamingStrategy.OPERATION_ID:
            tool_name = operation_id or f"{method.lower()}_{path.replace('/', '_')}"
        else:
            tool_name = f"{method.lower()}_{path.replace('/', '_').replace('{', '').replace('}', '').strip('_')}"

        tool_name = f"{self.config.tool_name_prefix}{tool_name}{self.config.tool_name_suffix}"

        # Create tool definition
        return {
            "name": tool_name,
            "description": operation.get("summary") or operation.get("description") or f"{method.upper()} {path}",
            "path": path,
            "method": method.upper(),
            "operation": operation,
            "operation_id": operation_id
        }

    def list_tools(self) -> List[Dict[str, Any]]:
        """List all generated MCP tools.

        Returns:
            List of tool definitions
        """
        return self._tools.copy()

    def get_api_info(self) -> Dict[str, Any]:
        """Get API information from the OpenAPI spec.

        Returns:
            API information
        """
        if not self._spec:
            raise ValueError("OpenAPI spec not loaded")

        info = self._spec.get("info", {})
        return {
            "title": info.get("title", "Unknown API"),
            "version": info.get("version", "Unknown"),
            "description": info.get("description", ""),
        }

    async def call_tool(self, name: str, **params) -> Any:
        """Call a tool directly.

        Args:
            name: Tool name
            **params: Tool parameters

        Returns:
            Tool result
        """
        # Find the tool
        tool = None
        for t in self._tools:
            if t["name"] == name:
                tool = t
                break

        if not tool:
            raise ValueError(f"Tool '{name}' not found")

        if not self._client:
            raise ValueError("Client not initialized. Call initialize() first.")

        # Transform parameters based on their location in the OpenAPI spec
        transformed_params = self._transform_parameters(tool["operation"], params)

        # Make the API request
        return await self._client.request(
            method=tool["method"],
            path=tool["path"],
            operation=tool["operation"],
            **transformed_params
        )

    def _transform_parameters(self, operation: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """Transform parameters based on their OpenAPI location."""
        transformed = {}
        operation_params = operation.get("parameters", [])

        # Create a mapping of parameter names to their locations
        param_locations = {}
        for param in operation_params:
            param_name = param["name"]
            param_location = param["in"]
            param_locations[param_name] = param_location

        # Transform parameters based on their location
        for param_name, param_value in params.items():
            location = param_locations.get(param_name, "query")  # Default to query

            if location == "path":
                transformed[f"path_{param_name}"] = param_value
            elif location == "header":
                transformed[f"header_{param_name}"] = param_value
            elif location == "query":
                transformed[f"query_{param_name}"] = param_value
            elif location == "cookie":
                transformed[f"cookie_{param_name}"] = param_value
            else:
                # For body parameters or unknown, pass as-is
                transformed[param_name] = param_value

        return transformed

    async def start(self):
        """Start the MCP server."""
        if not self._tools:
            raise ValueError("No tools generated. Call initialize() first.")

        # Create FastMCP server
        self._mcp_server = FastMCP(
            name=self.name,
            instructions=self.description
        )

        # Register tools
        for tool in self._tools:
            await self._register_tool(tool)

        print(f"🚀 MCP server '{self.name}' started with {len(self._tools)} tools")

    async def _register_tool(self, tool: Dict[str, Any]):
        """Register a tool with the MCP server."""
        if not self._mcp_server:
            raise ValueError("MCP server not created")

        # Create the tool function
        async def tool_func(**kwargs):
            return await self.call_tool(tool["name"], **kwargs)

        # Extract parameters from operation
        operation = tool["operation"]
        parameters = operation.get("parameters", [])

        # Build parameter schema
        param_schema = {
            "type": "object",
            "properties": {},
            "required": []
        }

        for param in parameters:
            param_name = param["name"]
            param_info = param.get("schema", {})

            prop_schema = {
                "type": param_info.get("type", "string"),
                "description": param.get("description", "")
            }

            if "enum" in param_info:
                prop_schema["enum"] = param_info["enum"]
            if "default" in param_info:
                prop_schema["default"] = param_info["default"]

            param_schema["properties"][param_name] = prop_schema

            if param.get("required", False):
                param_schema["required"].append(param_name)

        # Register with FastMCP
        self._mcp_server.tool(
            name=tool["name"],
            description=tool["description"],
            params=param_schema
        )(tool_func)


def create_openapi_mcp_server(
    openapi_url: str,
    name: str = "OpenAPI MCP Server",
    auth_config: Optional[Dict[str, Any]] = None,
    config: Optional[Dict[str, Any]] = None
) -> OpenAPIToMCPConverter:
    """Create an OpenAPI to MCP converter.

    Args:
        openapi_url: URL to OpenAPI specification
        name: Name for the MCP server
        auth_config: Authentication configuration
        config: Additional configuration options

    Returns:
        Converter instance
    """
    return OpenAPIToMCPConverter(
        openapi_url=openapi_url,
        name=name,
        auth_config=auth_config,
        config=config
    )