"""OpenAPI to MCP Converter.

A tool for converting OpenAPI specifications into MCP (Model Context Protocol) servers.
"""

from .converter import OpenAPIToMCPConverter, ConverterConfig, NamingStrategy, create_openapi_mcp_server
from .auth import AuthConfig, AuthType, AuthHandler
from .parser import OpenAPIParser
from .client import OpenAPIClient

__version__ = "0.1.0"

__all__ = [
    # Main converter
    "OpenAPIToMCPConverter",
    "ConverterConfig",
    "NamingStrategy",
    "create_openapi_mcp_server",

    # Authentication
    "AuthConfig",
    "AuthType",
    "AuthHandler",

    # Components
    "OpenAPIParser",
    "OpenAPIClient",
]