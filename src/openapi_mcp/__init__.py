"""OpenAPI to MCP Converter.

A tool for converting OpenAPI specifications into MCP (Model Context Protocol) servers.
"""

from .converter import OpenAPIToMCPConverter, ConverterConfig, NamingStrategy, create_openapi_mcp_server
from .auth import AuthConfig, AuthType, AuthHandler
from .parser import Open<PERSON><PERSON>ars<PERSON>
from .tool_generator import ToolGenerator
from .client import OpenAP<PERSON>lient
from .schema_handler import SchemaHandler

__version__ = "0.1.0"

__all__ = [
    # Main converter
    "OpenAPIToMCPConverter",
    "ConverterConfig",
    "NamingStrategy",
    "create_openapi_mcp_server",
    
    # Authentication
    "AuthConfig",
    "AuthType",
    "AuthHandler",
    
    # Components
    "OpenAPIParser",
    "ToolGenerator",
    "OpenAPIClient",
    "SchemaHandler",
]