"""OpenAPI specification parser."""

import json
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

try:
    import yaml
except ImportError:
    yaml = None  # type: ignore

try:
    from prance import ResolvingParser
except ImportError:
    ResolvingParser = None  # type: ignore

from .types import (
    OpenAPISpec,
    OpenAPIOperation,
    OpenAPIParameter,
    HTTPMethod,
    SecurityScheme,
)


class OpenAPIParser:
    """Parser for OpenAPI specifications."""

    def __init__(self, use_prance: bool = True):
        """Initialize the parser.
        
        Args:
            use_prance: Whether to use prance for reference resolution
        """
        self.use_prance = use_prance and ResolvingParser is not None

    def parse(self, spec_path: Union[str, Path]) -> OpenAPISpec:
        """Parse an OpenAPI specification from a file.
        
        Args:
            spec_path: Path to the OpenAPI specification file
            
        Returns:
            Parsed OpenAPI specification
        """
        spec_path = Path(spec_path)
        
        if self.use_prance:
            return self._parse_with_prance(spec_path)
        else:
            return self._parse_basic(spec_path)

    def parse_from_dict(self, spec_dict: Dict[str, Any]) -> OpenAPISpec:
        """Parse an OpenAPI specification from a dictionary.
        
        Args:
            spec_dict: OpenAPI specification as a dictionary
            
        Returns:
            Parsed OpenAPI specification
        """
        return OpenAPISpec(**spec_dict)

    def _parse_with_prance(self, spec_path: Path) -> OpenAPISpec:
        """Parse using prance for full reference resolution.
        
        Args:
            spec_path: Path to the OpenAPI specification file
            
        Returns:
            Parsed OpenAPI specification
        """
        parser = ResolvingParser(str(spec_path))
        return OpenAPISpec(**parser.specification)

    def _parse_basic(self, spec_path: Path) -> OpenAPISpec:
        """Basic parsing without reference resolution.
        
        Args:
            spec_path: Path to the OpenAPI specification file
            
        Returns:
            Parsed OpenAPI specification
        """
        content = spec_path.read_text()
        
        if spec_path.suffix in ['.yaml', '.yml']:
            if yaml is None:
                raise ImportError("PyYAML is required to parse YAML files. Install with: pip install pyyaml")
            spec_dict = yaml.safe_load(content)
        else:
            spec_dict = json.loads(content)
            
        return OpenAPISpec(**spec_dict)

    def extract_operations(self, spec: OpenAPISpec) -> List[Dict[str, Any]]:
        """Extract all operations from the specification.
        
        Args:
            spec: Parsed OpenAPI specification
            
        Returns:
            List of operations with metadata
        """
        operations = []
        
        for path, path_item in spec.paths.items():
            for method, operation in path_item.items():
                if method.upper() in [m.value for m in HTTPMethod]:
                    op_data = {
                        'path': path,
                        'method': HTTPMethod(method.upper()),
                        'operation': operation,
                        'operation_id': operation.get('operationId', f"{method}_{path.replace('/', '_')}"),
                    }
                    operations.append(op_data)
                    
        return operations

    def extract_security_schemes(self, spec: OpenAPISpec) -> Dict[str, SecurityScheme]:
        """Extract security schemes from the specification.
        
        Args:
            spec: Parsed OpenAPI specification
            
        Returns:
            Dictionary of security schemes
        """
        if not spec.components or 'securitySchemes' not in spec.components:
            return {}
            
        schemes = {}
        for name, scheme_data in spec.components['securitySchemes'].items():
            schemes[name] = SecurityScheme(**scheme_data)
            
        return schemes

    def resolve_parameters(
        self, 
        operation: Dict[str, Any], 
        spec: OpenAPISpec
    ) -> List[OpenAPIParameter]:
        """Resolve parameters for an operation.
        
        Args:
            operation: Operation data
            spec: OpenAPI specification
            
        Returns:
            List of resolved parameters
        """
        parameters = []
        
        # Direct parameters
        for param in operation.get('parameters', []):
            if '$ref' in param:
                # Resolve reference
                param = self._resolve_reference(param['$ref'], spec)
            parameters.append(OpenAPIParameter(**param))
            
        return parameters

    def _resolve_reference(self, ref: str, spec: OpenAPISpec) -> Dict[str, Any]:
        """Resolve a JSON reference.
        
        Args:
            ref: Reference string (e.g., '#/components/parameters/Id')
            spec: OpenAPI specification
            
        Returns:
            Resolved reference data
        """
        if not ref.startswith('#/'):
            raise ValueError(f"Only local references are supported, got: {ref}")
            
        parts = ref[2:].split('/')
        current = spec.dict()
        
        for part in parts:
            if part in current:
                current = current[part]
            else:
                raise ValueError(f"Cannot resolve reference: {ref}")
                
        return current