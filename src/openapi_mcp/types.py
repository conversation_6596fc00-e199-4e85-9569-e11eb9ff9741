"""Type definitions for OpenAPI to MCP converter."""

from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field


class HTTPMethod(str, Enum):
    """HTTP methods supported by the converter."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class AuthType(str, Enum):
    """Authentication types supported."""
    API_KEY = "apiKey"
    HTTP = "http"
    OAUTH2 = "oauth2"
    OPENID_CONNECT = "openIdConnect"


class ParameterLocation(str, Enum):
    """OpenAPI parameter locations."""
    QUERY = "query"
    HEADER = "header"
    PATH = "path"
    COOKIE = "cookie"


class OpenAPIParameter(BaseModel):
    """OpenAPI parameter definition."""
    name: str
    location: ParameterLocation = Field(alias="in")
    description: Optional[str] = None
    required: bool = False
    schema_: Dict[str, Any] = Field(alias="schema", default_factory=dict)
    example: Optional[Any] = None


class OpenAPIOperation(BaseModel):
    """OpenAPI operation definition."""
    operation_id: str = Field(alias="operationId")
    summary: Optional[str] = None
    description: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    parameters: List[OpenAPIParameter] = Field(default_factory=list)
    request_body: Optional[Dict[str, Any]] = Field(alias="requestBody", default=None)
    responses: Dict[str, Any] = Field(default_factory=dict)
    security: Optional[List[Dict[str, List[str]]]] = None


class OpenAPIPath(BaseModel):
    """OpenAPI path item."""
    path: str
    methods: Dict[HTTPMethod, OpenAPIOperation]


class SecurityScheme(BaseModel):
    """OpenAPI security scheme definition."""
    type: AuthType
    description: Optional[str] = None
    name: Optional[str] = None  # For API key
    location: Optional[ParameterLocation] = Field(alias="in", default=None)  # For API key
    scheme: Optional[str] = None  # For HTTP auth
    flows: Optional[Dict[str, Any]] = None  # For OAuth2


class OpenAPISpec(BaseModel):
    """Parsed OpenAPI specification."""
    openapi: str
    info: Dict[str, Any]
    servers: List[Dict[str, Any]] = Field(default_factory=list)
    paths: Dict[str, Dict[str, Any]]
    components: Optional[Dict[str, Any]] = None
    security: Optional[List[Dict[str, List[str]]]] = None
    tags: Optional[List[Dict[str, Any]]] = None


class MCPToolParameter(BaseModel):
    """MCP tool parameter definition."""
    name: str
    description: str
    type: str
    required: bool = False
    default: Optional[Any] = None
    enum: Optional[List[Any]] = None


class MCPTool(BaseModel):
    """MCP tool definition."""
    name: str
    description: str
    parameters: List[MCPToolParameter]
    method: HTTPMethod
    path: str
    operation_id: str
    requires_auth: bool = False


class ConversionConfig(BaseModel):
    """Configuration for OpenAPI to MCP conversion."""
    base_url: Optional[str] = None
    auth_config: Optional[Dict[str, Any]] = None
    include_tags: Optional[List[str]] = None
    exclude_tags: Optional[List[str]] = None
    include_operations: Optional[List[str]] = None
    exclude_operations: Optional[List[str]] = None
    parameter_style: str = "snake_case"  # or "camelCase"
    max_description_length: Optional[int] = None