"""MCP tool generator from OpenAPI operations."""

from typing import Any, Dict, List, Optional
import re
from fastmcp import Context, MCP
from pydantic import BaseModel

from .types import (
    MCPTool,
    MCPToolParameter,
    OpenAPIOperation,
    OpenAPIParameter,
    HTTPMethod,
    ConversionConfig,
)
from .client import HTTPClient


class MCPToolGenerator:
    """Generator for MCP tools from OpenAPI operations."""

    def __init__(self, config: Optional[ConversionConfig] = None):
        """Initialize the generator.
        
        Args:
            config: Optional conversion configuration
        """
        self.config = config or ConversionConfig()

    def generate_tool_name(self, operation_id: str, method: HTTPMethod, path: str) -> str:
        """Generate a tool name from operation details.
        
        Args:
            operation_id: OpenAPI operation ID
            method: HTTP method
            path: API path
            
        Returns:
            Generated tool name
        """
        if operation_id:
            # Clean operation ID
            name = re.sub(r'[^a-zA-Z0-9_]', '_', operation_id)
        else:
            # Generate from method and path
            path_parts = path.strip('/').split('/')
            path_parts = [p for p in path_parts if not p.startswith('{')]
            name = f"{method.value.lower()}_{'_'.join(path_parts)}"
        
        # Convert to snake_case if configured
        if self.config.parameter_style == "snake_case":
            name = self._to_snake_case(name)
        
        return name

    def generate_tool_description(
        self, 
        operation: Dict[str, Any],
        method: HTTPMethod,
        path: str
    ) -> str:
        """Generate a tool description from operation details.
        
        Args:
            operation: OpenAPI operation data
            method: HTTP method
            path: API path
            
        Returns:
            Generated description
        """
        if operation.get('summary'):
            description = operation['summary']
        elif operation.get('description'):
            description = operation['description']
        else:
            description = f"{method.value} {path}"
        
        # Truncate if needed
        if self.config.max_description_length and len(description) > self.config.max_description_length:
            description = description[:self.config.max_description_length - 3] + "..."
        
        return description

    def generate_parameters(
        self,
        parameters: List[OpenAPIParameter],
        request_body: Optional[Dict[str, Any]] = None
    ) -> List[MCPToolParameter]:
        """Generate MCP tool parameters from OpenAPI parameters.
        
        Args:
            parameters: List of OpenAPI parameters
            request_body: Optional request body definition
            
        Returns:
            List of MCP tool parameters
        """
        mcp_params = []
        
        # Path and query parameters
        for param in parameters:
            if param.location in ['path', 'query']:
                mcp_param = MCPToolParameter(
                    name=self._convert_param_name(param.name),
                    description=param.description or f"{param.name} parameter",
                    type=self._convert_type(param.schema_),
                    required=param.required,
                    default=param.schema_.get('default'),
                    enum=param.schema_.get('enum')
                )
                mcp_params.append(mcp_param)
        
        # Request body parameters
        if request_body and 'content' in request_body:
            # Handle JSON request body
            if 'application/json' in request_body['content']:
                schema = request_body['content']['application/json'].get('schema', {})
                if 'properties' in schema:
                    for prop_name, prop_schema in schema['properties'].items():
                        required = prop_name in schema.get('required', [])
                        mcp_param = MCPToolParameter(
                            name=self._convert_param_name(prop_name),
                            description=prop_schema.get('description', f"{prop_name} field"),
                            type=self._convert_type(prop_schema),
                            required=required,
                            default=prop_schema.get('default'),
                            enum=prop_schema.get('enum')
                        )
                        mcp_params.append(mcp_param)
        
        return mcp_params

    def generate_tool(
        self,
        operation_data: Dict[str, Any],
        http_client: Optional[HTTPClient] = None
    ) -> MCPTool:
        """Generate an MCP tool from an OpenAPI operation.
        
        Args:
            operation_data: Operation data including path, method, and operation
            http_client: Optional HTTP client for making requests
            
        Returns:
            Generated MCP tool
        """
        path = operation_data['path']
        method = operation_data['method']
        operation = operation_data['operation']
        operation_id = operation_data['operation_id']
        
        # Check if we should include this operation
        if not self._should_include_operation(operation, operation_id):
            return None
        
        # Generate tool components
        tool_name = self.generate_tool_name(operation_id, method, path)
        tool_description = self.generate_tool_description(operation, method, path)
        
        # Parse parameters
        parameters = []
        for param_data in operation.get('parameters', []):
            parameters.append(OpenAPIParameter(**param_data))
        
        tool_parameters = self.generate_parameters(
            parameters,
            operation.get('requestBody')
        )
        
        # Check if authentication is required
        requires_auth = bool(operation.get('security')) or bool(operation.get('security', []))
        
        return MCPTool(
            name=tool_name,
            description=tool_description,
            parameters=tool_parameters,
            method=method,
            path=path,
            operation_id=operation_id,
            requires_auth=requires_auth
        )

    def create_mcp_function(self, tool: MCPTool, http_client: HTTPClient):
        """Create an MCP function for a tool.
        
        Args:
            tool: MCP tool definition
            http_client: HTTP client for making requests
            
        Returns:
            Function to be used as MCP tool
        """
        async def mcp_function(ctx: Context, **kwargs):
            """Generated MCP function."""
            # Separate path params from query params
            path_params = {}
            query_params = {}
            body_data = {}
            
            # Process the path to extract path parameters
            path_pattern = tool.path
            for param in tool.parameters:
                param_value = kwargs.get(param.name)
                if param_value is not None:
                    if f"{{{param.name}}}" in tool.path:
                        # It's a path parameter
                        path_params[param.name] = param_value
                        path_pattern = path_pattern.replace(
                            f"{{{param.name}}}", 
                            str(param_value)
                        )
                    else:
                        # Check if it's likely a body parameter
                        if tool.method in [HTTPMethod.POST, HTTPMethod.PUT, HTTPMethod.PATCH]:
                            body_data[param.name] = param_value
                        else:
                            query_params[param.name] = param_value
            
            # Make the request
            response = await http_client.arequest(
                method=tool.method.value,
                path=path_pattern,
                params=query_params if query_params else None,
                json_data=body_data if body_data else None
            )
            
            # Return response data
            if response.headers.get('content-type', '').startswith('application/json'):
                return response.json()
            else:
                return response.text
        
        # Set function metadata
        mcp_function.__name__ = tool.name
        mcp_function.__doc__ = tool.description
        
        return mcp_function

    def _should_include_operation(
        self, 
        operation: Dict[str, Any], 
        operation_id: str
    ) -> bool:
        """Check if an operation should be included based on config.
        
        Args:
            operation: Operation data
            operation_id: Operation ID
            
        Returns:
            Whether to include the operation
        """
        # Check operation ID filters
        if self.config.include_operations:
            if operation_id not in self.config.include_operations:
                return False
        elif self.config.exclude_operations:
            if operation_id in self.config.exclude_operations:
                return False
        
        # Check tag filters
        tags = operation.get('tags', [])
        if self.config.include_tags:
            if not any(tag in self.config.include_tags for tag in tags):
                return False
        elif self.config.exclude_tags:
            if any(tag in self.config.exclude_tags for tag in tags):
                return False
        
        return True

    def _convert_param_name(self, name: str) -> str:
        """Convert parameter name based on config.
        
        Args:
            name: Original parameter name
            
        Returns:
            Converted parameter name
        """
        if self.config.parameter_style == "snake_case":
            return self._to_snake_case(name)
        return name

    def _to_snake_case(self, name: str) -> str:
        """Convert a string to snake_case.
        
        Args:
            name: String to convert
            
        Returns:
            Snake case string
        """
        # Replace hyphens with underscores
        name = name.replace('-', '_')
        # Insert underscore before uppercase letters
        name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', name)
        return name.lower()

    def _convert_type(self, schema: Dict[str, Any]) -> str:
        """Convert OpenAPI schema type to Python type string.
        
        Args:
            schema: OpenAPI schema
            
        Returns:
            Python type string
        """
        oa_type = schema.get('type', 'string')
        
        type_map = {
            'string': 'str',
            'integer': 'int',
            'number': 'float',
            'boolean': 'bool',
            'array': 'list',
            'object': 'dict'
        }
        
        return type_map.get(oa_type, 'str')