"""HTTP client for making OpenAPI requests."""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable, Union
from urllib.parse import urljoin, urlencode

import httpx
from httpx import Response

from .auth import AuthHandler

logger = logging.getLogger(__name__)


class OpenAPIClient:
    """HTTP client for making OpenAPI requests."""
    
    def __init__(
        self,
        base_url: str,
        auth_handler: Optional[AuthHandler] = None,
        timeout: float = 30.0,
        max_retries: int = 3,
        follow_redirects: bool = True,
        request_interceptor: Optional[Callable] = None,
        response_interceptor: Optional[Callable] = None
    ):
        """Initialize the OpenAPI client.
        
        Args:
            base_url: Base URL for API requests
            auth_handler: Authentication handler
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            follow_redirects: Whether to follow redirects
            request_interceptor: Function to intercept/modify requests
            response_interceptor: Function to intercept/modify responses
        """
        self.base_url = base_url.rstrip('/')
        self.auth_handler = auth_handler
        self.timeout = timeout
        self.max_retries = max_retries
        self.follow_redirects = follow_redirects
        self.request_interceptor = request_interceptor
        self.response_interceptor = response_interceptor
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(timeout),
            follow_redirects=follow_redirects
        )
    
    async def request(
        self,
        method: str,
        path: str,
        operation: Dict[str, Any],
        **kwargs
    ) -> Any:
        """Make an API request based on operation definition.
        
        Args:
            method: HTTP method
            path: Request path
            operation: OpenAPI operation definition
            **kwargs: Parameters from tool invocation
            
        Returns:
            Response data
        """
        # Build request components
        url = self._build_url(path, kwargs)
        headers = self._build_headers(kwargs)
        params = self._build_query_params(kwargs)
        body = self._build_body(kwargs)
        
        # Apply authentication
        if self.auth_handler:
            auth_headers = await self.auth_handler.get_auth_headers()
            headers.update(auth_headers)
        
        # Prepare request
        request_kwargs = {
            "method": method,
            "url": url,
            "headers": headers,
            "params": params
        }
        
        # Add body if present
        if body is not None:
            if isinstance(body, dict):
                request_kwargs["json"] = body
            else:
                request_kwargs["data"] = body
        
        # Apply request interceptor
        if self.request_interceptor:
            request_kwargs = await self._apply_interceptor(
                self.request_interceptor,
                request_kwargs
            )
        
        # Make request with retries
        last_error = None
        for attempt in range(self.max_retries):
            try:
                response = await self.client.request(**request_kwargs)
                
                # Apply response interceptor
                if self.response_interceptor:
                    response = await self._apply_interceptor(
                        self.response_interceptor,
                        response
                    )
                
                # Check response status
                response.raise_for_status()
                
                # Parse response
                return self._parse_response(response)
                
            except httpx.TimeoutException as e:
                last_error = e
                logger.warning(f"Request timeout (attempt {attempt + 1}/{self.max_retries}): {e}")
                
            except httpx.HTTPStatusError as e:
                # Don't retry client errors (4xx)
                if 400 <= e.response.status_code < 500:
                    raise
                
                last_error = e
                logger.warning(f"HTTP error (attempt {attempt + 1}/{self.max_retries}): {e}")
                
            except Exception as e:
                last_error = e
                logger.error(f"Request error (attempt {attempt + 1}/{self.max_retries}): {e}")
            
            # Wait before retry (exponential backoff)
            if attempt < self.max_retries - 1:
                wait_time = (2 ** attempt) * 1
                await asyncio.sleep(wait_time)
        
        # All retries failed
        raise last_error or Exception("Request failed after all retries")
    
    def _build_url(self, path: str, params: Dict[str, Any]) -> str:
        """Build the request URL with path parameters.

        Args:
            path: URL path template
            params: Parameters dictionary

        Returns:
            Formatted URL
        """
        # Replace path parameters
        for key, value in params.items():
            if key.startswith("path_"):
                param_name = key[5:]  # Remove "path_" prefix
                path = path.replace(f"{{{param_name}}}", str(value))

        # Combine with base URL
        return urljoin(self.base_url + "/", path.lstrip("/"))
    
    def _build_headers(self, params: Dict[str, Any]) -> Dict[str, str]:
        """Build request headers.
        
        Args:
            params: Parameters dictionary
            
        Returns:
            Headers dictionary
        """
        headers = {
            "User-Agent": "OpenAPI-MCP-Converter/1.0",
            "Accept": "application/json"
        }
        
        # Add header parameters
        for key, value in params.items():
            if key.startswith("header_"):
                header_name = key[7:]  # Remove "header_" prefix
                headers[header_name] = str(value)
        
        # Handle bearer token
        if "bearer_token" in params:
            headers["Authorization"] = f"Bearer {params['bearer_token']}"
        
        return headers
    
    def _build_query_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Build query parameters.
        
        Args:
            params: Parameters dictionary
            
        Returns:
            Query parameters dictionary
        """
        query_params = {}
        
        for key, value in params.items():
            if key.startswith("query_"):
                param_name = key[6:]  # Remove "query_" prefix
                query_params[param_name] = value
        
        return query_params
    
    def _build_body(self, params: Dict[str, Any]) -> Optional[Union[Dict, str, bytes]]:
        """Build request body.
        
        Args:
            params: Parameters dictionary
            
        Returns:
            Request body
        """
        if "body" in params:
            return params["body"]
        
        # Check for form data parameters
        form_data = {}
        for key, value in params.items():
            if key.startswith("form_"):
                param_name = key[5:]  # Remove "form_" prefix
                form_data[param_name] = value
        
        if form_data:
            return form_data
        
        return None
    
    async def _apply_interceptor(self, interceptor: Callable, data: Any) -> Any:
        """Apply an interceptor function.
        
        Args:
            interceptor: Interceptor function
            data: Data to pass to interceptor
            
        Returns:
            Modified data
        """
        if asyncio.iscoroutinefunction(interceptor):
            return await interceptor(data)
        else:
            return interceptor(data)
    
    def _parse_response(self, response: Response) -> Any:
        """Parse response based on content type.
        
        Args:
            response: HTTP response
            
        Returns:
            Parsed response data
        """
        content_type = response.headers.get("content-type", "").lower()
        
        if "application/json" in content_type:
            return response.json()
        elif "text/" in content_type:
            return response.text
        elif response.content:
            # Return raw bytes for binary content
            return response.content
        else:
            return None
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()