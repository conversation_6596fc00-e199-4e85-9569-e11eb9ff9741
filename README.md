# OpenAPI to MCP Converter

🚀 **Convert any OpenAPI specification into a Model Context Protocol (MCP) server automatically!**

This project provides a dynamic converter that takes an OpenAPI (Swagger) specification and generates a fully functional MCP server, exposing all API endpoints as MCP tools that can be used by AI agents and other MCP clients.

## ✨ Features

- 🔄 **Automatic Conversion**: Point to any OpenAPI spec URL and get an MCP server
- 🔐 **Full Authentication Support**: Bearer tokens, API keys, Basic auth, OAuth2
- 🏷️ **Smart Tool Naming**: Multiple strategies for clean, usable tool names
- 🎯 **Flexible Filtering**: Include/exclude by tags, paths, or HTTP methods
- 🚦 **Rate Limiting**: Built-in rate limiting and retry logic
- 📦 **Type Safety**: Full type hints and Pydantic validation
- ⚡ **Async First**: Built on httpx for optimal performance
- 🛡️ **Error Handling**: Graceful error handling with MCP-compliant error formats

## 🎯 Use Cases

- **AI Agents**: Give LLMs access to any REST API via MCP tools
- **API Integration**: Unified interface for multiple APIs
- **Development**: Quickly prototype MCP servers from existing APIs
- **Testing**: Test MCP clients against real-world APIs

## 📦 Installation

```bash
pip install openapi-mcp-converter
```

Or install from source:

```bash
git clone https://github.com/yourusername/openapi-mcp-converter.git
cd openapi-mcp-converter
pip install -e .
```

## 🚀 Quick Start

### Basic Usage

```python
from openapi_mcp import OpenAPIToMCPConverter

# Create converter
converter = OpenAPIToMCPConverter(
    openapi_url="https://api.example.com/openapi.json",
    name="Example API"
)

# Initialize and start
await converter.initialize()
await converter.start()
```

### With Authentication

```python
# Bearer token
converter = OpenAPIToMCPConverter(
    openapi_url="https://api.example.com/openapi.json",
    name="Example API",
    auth_config={
        "type": "bearer",
        "token": "your-api-token"
    }
)

# API Key
converter = OpenAPIToMCPConverter(
    openapi_url="https://api.example.com/openapi.json",
    name="Example API",
    auth_config={
        "type": "apiKey",
        "location": "header",
        "name": "X-API-Key",
        "value": "your-api-key"
    }
)
```

### Advanced Configuration

```python
converter = OpenAPIToMCPConverter(
    openapi_url="https://api.example.com/openapi.json",
    name="Example API",
    auth_config=auth_config,
    config={
        # Tool naming
        "tool_name_prefix": "example_",
        "naming_strategy": "operationId",  # or "path_method"
        
        # Filtering
        "include_tags": ["Users", "Posts"],
        "exclude_patterns": ["*/admin/*", "*/internal/*"],
        "include_methods": ["GET", "POST"],
        
        # HTTP client settings
        "timeout": 30.0,
        "max_retries": 3,
        "retry_delay": 1.0,
        
        # Rate limiting
        "rate_limit": {
            "requests_per_second": 10,
            "burst": 20
        }
    }
)
```

## 🏥 Real-World Example: PulseTrack Medical API

```python
from openapi_mcp import OpenAPIToMCPConverter

# Convert PulseTrack medical platform API to MCP
converter = OpenAPIToMCPConverter(
    openapi_url="http://pulsetrack-api.synapsedx.co.uk:8000/openapi.json",
    name="PulseTrack Medical Platform",
    auth_config={
        "type": "bearer",
        "token": os.getenv("PULSETRACK_API_KEY")
    },
    config={
        "tool_name_prefix": "pulsetrack_",
        "include_tags": ["Patients", "Clinicians", "Appointments"],
        "timeout": 30.0
    }
)

await converter.initialize()

# List available tools
tools = converter.list_tools()
print(f"Generated {len(tools)} MCP tools")

# Call a tool
result = await converter.call_tool("pulsetrack_get_patient_profile")
```

## 🛠️ API Reference

### OpenAPIToMCPConverter

Main converter class that orchestrates the conversion process.

#### Constructor Parameters

- `openapi_url` (str): URL or file path to OpenAPI specification
- `name` (str): Name for the MCP server
- `description` (str, optional): Description of the MCP server
- `auth_config` (dict, optional): Authentication configuration
- `config` (dict, optional): Additional configuration options

#### Methods

- `async initialize()`: Initialize the converter and parse OpenAPI spec
- `async start()`: Start the MCP server
- `list_tools()`: Get list of generated MCP tools
- `async call_tool(name, **params)`: Call a tool directly
- `get_api_info()`: Get information about the API

### Configuration Options

#### Tool Naming
- `tool_name_prefix`: Prefix for all tool names
- `tool_name_suffix`: Suffix for all tool names
- `naming_strategy`: Strategy for generating tool names
  - `"operationId"`: Use OpenAPI operationId
  - `"path_method"`: Combine HTTP method and path

#### Filtering
- `include_tags`: List of tags to include
- `exclude_tags`: List of tags to exclude
- `include_patterns`: Path patterns to include
- `exclude_patterns`: Path patterns to exclude
- `include_methods`: HTTP methods to include
- `exclude_methods`: HTTP methods to exclude

#### HTTP Client
- `timeout`: Request timeout in seconds
- `max_retries`: Maximum retry attempts
- `retry_delay`: Initial retry delay
- `retry_strategy`: "exponential", "linear", or "fixed"
- `verify_ssl`: SSL verification (default: true)

#### Rate Limiting
- `rate_limit.requests_per_second`: Request rate limit
- `rate_limit.burst`: Burst capacity

## 🏗️ Architecture

The converter consists of several components:

1. **Parser**: Parses and validates OpenAPI specifications
2. **Tool Generator**: Converts OpenAPI operations to MCP tools
3. **HTTP Client**: Handles API requests with auth and retries
4. **Auth Handler**: Manages various authentication schemes
5. **Schema Handler**: Resolves JSON Schema references
6. **MCP Server**: Serves the generated tools via MCP protocol

## 🧪 Testing

Run the test suite:

```bash
pytest tests/
```

Run with coverage:

```bash
pytest tests/ --cov=openapi_mcp --cov-report=html
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [FastMCP](https://github.com/jlowin/fastmcp) for MCP server functionality
- Uses [httpx](https://www.python-httpx.org/) for async HTTP
- Powered by [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation
- OpenAPI parsing via custom implementation

## 📮 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our server](https://discord.gg/example)
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/openapi-mcp-converter/issues)

---

Made with ❤️ for the MCP community