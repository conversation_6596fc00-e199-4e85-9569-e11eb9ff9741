#!/usr/bin/env python3
"""Debug URL construction issue."""

import asyncio
from openapi_mcp import OpenAPIToMCPConverter

async def main():
    """Debug the URL construction."""
    
    # Use the public Petstore API for testing
    petstore_url = "https://petstore3.swagger.io/api/v3/openapi.json"
    
    print("🐾 Debugging URL construction...")
    
    # Create converter
    converter = OpenAPIToMCPConverter(
        openapi_url=petstore_url,
        name="Petstore API",
        config={
            "tool_name_prefix": "pet_",
            "naming_strategy": "operationId",
            "timeout": 10.0,
        }
    )
    
    # Initialize
    print("\n📦 Initializing converter...")
    await converter.initialize()
    
    # Check the base URL
    print(f"\n🔗 Base URL: {converter._client.base_url}")
    
    # Check the spec servers
    print(f"\n🖥️ Servers in spec: {converter._spec.get('servers', [])}")
    
    # Find a simple tool to test
    tools = converter.list_tools()
    test_tool = None
    for tool in tools:
        if tool["name"] == "pet_findPetsByStatus":
            test_tool = tool
            break
    
    if test_tool:
        print(f"\n🔧 Testing tool: {test_tool['name']}")
        print(f"   Path: {test_tool['path']}")
        print(f"   Method: {test_tool['method']}")
        
        # Test URL construction manually
        from urllib.parse import urljoin
        base_url = converter._client.base_url
        path = test_tool['path']
        
        print(f"\n🔗 URL construction:")
        print(f"   Base URL: {base_url}")
        print(f"   Path: {path}")
        print(f"   urljoin result: {urljoin(base_url + '/', path.lstrip('/'))}")
        
        # Try the actual call
        try:
            print(f"\n🧪 Testing actual call...")
            result = await converter.call_tool("pet_findPetsByStatus", status="available")
            print(f"✅ Success! Found {len(result) if isinstance(result, list) else 'some'} results")
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
