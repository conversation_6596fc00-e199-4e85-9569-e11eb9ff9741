#!/usr/bin/env python3
"""
Test PulseTrack API with real API key
"""

import asyncio
from openapi_mcp import OpenAPIToMCPConverter

async def main():
    """Test PulseTrack API with authentication."""
    
    # Your PulseTrack API key
    api_key = "pk_live_0Jt9rPfIg6SVR3l8eqq8biELPR62THApGWs1JX57"
    
    print("🏥 Testing PulseTrack API with real authentication...")
    
    # Create converter with Bearer token authentication
    converter = OpenAPIToMCPConverter(
        openapi_url="http://pulsetrack-api.synapsedx.co.uk:8000/openapi.json",
        name="PulseTrack Medical Platform",
        description="MCP interface for PulseTrack healthcare API",
        auth_config={
            "type": "bearer",
            "token": api_key
        },
        config={
            "tool_name_prefix": "pulsetrack_",
            "naming_strategy": "operationId",
            "timeout": 30.0,
            "max_retries": 3,
        }
    )
    
    # Initialize
    print("\n📦 Initializing converter...")
    await converter.initialize()
    
    # Get API info
    info = converter.get_api_info()
    print(f"\n📊 API Information:")
    print(f"  - Title: {info.get('title', 'N/A')}")
    print(f"  - Version: {info.get('version', 'N/A')}")
    print(f"  - Description: {info.get('description', 'N/A')}")
    
    # List tools
    tools = converter.list_tools()
    print(f"\n🔧 Generated {len(tools)} MCP tools")
    
    # Show some example tools
    print("\nSample tools:")
    for tool in tools[:10]:
        print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
    
    # Show clinician-specific tools
    print("\nClinician-specific tools:")
    clinician_tools = [tool for tool in tools if 'clinician' in tool['name'].lower() or 'clinic' in tool['name'].lower()]
    for tool in clinician_tools[:10]:
        print(f"  - {tool['name']}: {tool.get('description', 'No description')}")

    # Test some API calls with clinician access
    print("\n\n🧪 Testing API calls with clinician authentication:")

    # Test 1: Get clinician profile/info
    try:
        print("\n1️⃣ Testing clinician profile retrieval...")
        # Look for clinician profile endpoints
        clinician_profile_tools = [tool for tool in tools if 'clinician' in tool['name'].lower() and ('me' in tool['name'] or 'profile' in tool['name'])]
        if clinician_profile_tools:
            tool_name = clinician_profile_tools[0]['name']
            result = await converter.call_tool(tool_name)
            print(f"   ✅ Clinician profile retrieved successfully!")
            print(f"   👨‍⚕️ Profile data: {result}")
        else:
            print("   ℹ️ No clinician profile endpoint found")
    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 2: List patients (if available)
    try:
        print("\n2️⃣ Testing patient list retrieval...")
        # Look for patient listing endpoints
        patient_list_tools = [tool for tool in tools if 'patient' in tool['name'].lower() and ('list' in tool['name'] or 'get' in tool['name']) and 'me' not in tool['name']]
        if patient_list_tools:
            tool_name = patient_list_tools[0]['name']
            print(f"   🔍 Using tool: {tool_name}")
            result = await converter.call_tool(tool_name)
            print(f"   ✅ Patient list retrieved successfully!")
            print(f"   👥 Patients: {result}")
        else:
            print("   ℹ️ No patient list endpoint found")
    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 3: Get clinic information
    try:
        print("\n3️⃣ Testing clinic information...")
        clinic_tools = [tool for tool in tools if 'clinic' in tool['name'].lower() and 'get' in tool['name']]
        if clinic_tools:
            tool_name = clinic_tools[0]['name']
            print(f"   🔍 Using tool: {tool_name}")
            result = await converter.call_tool(tool_name)
            print(f"   ✅ Clinic info retrieved successfully!")
            print(f"   🏥 Clinic data: {result}")
        else:
            print("   ℹ️ No clinic info endpoint found")
    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 4: Get medications (clinician view)
    try:
        print("\n4️⃣ Testing medication management...")
        med_tools = [tool for tool in tools if 'medication' in tool['name'].lower() and 'get' in tool['name'] and 'me' not in tool['name']]
        if med_tools:
            tool_name = med_tools[0]['name']
            print(f"   🔍 Using tool: {tool_name}")
            result = await converter.call_tool(tool_name)
            print(f"   ✅ Medications retrieved successfully!")
            print(f"   💊 Medications: {result}")
        else:
            print("   ℹ️ No medication management endpoint found")
    except Exception as e:
        print(f"   ❌ Error: {e}")

    # Test 5: Get appointments (clinician view)
    try:
        print("\n5️⃣ Testing appointment management...")
        appt_tools = [tool for tool in tools if 'appointment' in tool['name'].lower() and 'get' in tool['name'] and 'me' not in tool['name']]
        if appt_tools:
            tool_name = appt_tools[0]['name']
            print(f"   🔍 Using tool: {tool_name}")
            result = await converter.call_tool(tool_name)
            print(f"   ✅ Appointments retrieved successfully!")
            print(f"   📅 Appointments: {result}")
        else:
            print("   ℹ️ No appointment management endpoint found")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print(f"\n\n✨ PulseTrack API testing complete!")
    print(f"🎯 The converter successfully created {len(tools)} MCP tools from the PulseTrack API")
    print("🚀 These tools can now be used by AI agents through the MCP protocol!")

if __name__ == "__main__":
    asyncio.run(main())
