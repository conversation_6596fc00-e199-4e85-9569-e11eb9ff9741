#!/usr/bin/env python3
"""
Test PulseTrack API with real API key
"""

import asyncio
from openapi_mcp import OpenAPIToMCPConverter

async def main():
    """Test PulseTrack API with authentication."""
    
    # Your PulseTrack API key
    api_key = "pk_live_0Jt9rPfIg6SVR3l8eqq8biELPR62THApGWs1JX57"
    
    print("🏥 Testing PulseTrack API with real authentication...")
    
    # Create converter with Bearer token authentication
    converter = OpenAPIToMCPConverter(
        openapi_url="http://pulsetrack-api.synapsedx.co.uk:8000/openapi.json",
        name="PulseTrack Medical Platform",
        description="MCP interface for PulseTrack healthcare API",
        auth_config={
            "type": "bearer",
            "token": api_key
        },
        config={
            "tool_name_prefix": "pulsetrack_",
            "naming_strategy": "operationId",
            "timeout": 30.0,
            "max_retries": 3,
        }
    )
    
    # Initialize
    print("\n📦 Initializing converter...")
    await converter.initialize()
    
    # Get API info
    info = converter.get_api_info()
    print(f"\n📊 API Information:")
    print(f"  - Title: {info.get('title', 'N/A')}")
    print(f"  - Version: {info.get('version', 'N/A')}")
    print(f"  - Description: {info.get('description', 'N/A')}")
    
    # List tools
    tools = converter.list_tools()
    print(f"\n🔧 Generated {len(tools)} MCP tools")
    
    # Show some example tools
    print("\nSample tools:")
    for tool in tools[:10]:
        print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
    
    # Test some API calls
    print("\n\n🧪 Testing API calls with authentication:")
    
    # Test 1: Get current patient profile
    try:
        print("\n1️⃣ Testing patient profile retrieval...")
        result = await converter.call_tool("pulsetrack_read_patient_me_api_v1_patients_me_get")
        print(f"   ✅ Patient profile retrieved successfully!")
        print(f"   📋 Profile data: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Get medications
    try:
        print("\n2️⃣ Testing medication list retrieval...")
        result = await converter.call_tool("pulsetrack_get_clinic_medications_for_patient_api_v1_patients_me_medications_get")
        print(f"   ✅ Medications retrieved successfully!")
        print(f"   💊 Medications: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Get appointments
    try:
        print("\n3️⃣ Testing appointment retrieval...")
        result = await converter.call_tool("pulsetrack_read_patient_appointments_api_v1_patients_me_appointments_get")
        print(f"   ✅ Appointments retrieved successfully!")
        print(f"   📅 Appointments: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Get weight log history
    try:
        print("\n4️⃣ Testing weight log history...")
        result = await converter.call_tool("pulsetrack_read_weight_log_history_api_v1_patients_me_weight_log_get")
        print(f"   ✅ Weight history retrieved successfully!")
        print(f"   ⚖️ Weight data: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 5: Get side effects
    try:
        print("\n5️⃣ Testing side effects retrieval...")
        result = await converter.call_tool("pulsetrack_read_patient_side_effects_api_v1_patients_me_side_effects_get")
        print(f"   ✅ Side effects retrieved successfully!")
        print(f"   🩺 Side effects: {result}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print(f"\n\n✨ PulseTrack API testing complete!")
    print(f"🎯 The converter successfully created {len(tools)} MCP tools from the PulseTrack API")
    print("🚀 These tools can now be used by AI agents through the MCP protocol!")

if __name__ == "__main__":
    asyncio.run(main())
